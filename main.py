import os
import sys
import psutil
import subprocess
import threading
import time
import tkinter as tk
from tkinter import ttk
import webbrowser
from PIL import Image, ImageTk, ImageSequence
import urllib.request
import zipfile
from tkinter import messagebox
from tkinter.ttk import Progressbar
import re
import requests
import json

from packaging import version
import math
import secrets
import hashlib
import json
import socket
import struct

def natural_sort_key(text):
    """
    Convert a string into a list of mixed strings and integers for natural sorting.
    This makes 'test.zip' come first, then 'test1.zip', 'test2.zip', etc.
    """
    import re
    def convert(text):
        return int(text) if text.isdigit() else text.lower()

    # Split the text into parts
    parts = [convert(c) for c in re.split('([0-9]+)', text)]

    # Special handling for files without numbers (like 'test.zip')
    # If we only have one part (no numbers found), add a 0 to make it sort first
    if len(parts) == 1:
        # Split on the dot to separate name and extension
        name_parts = text.split('.')
        if len(name_parts) >= 2:
            # Return [basename, 0, extension] to make it sort before numbered versions
            return [name_parts[0].lower(), 0, '.' + '.'.join(name_parts[1:])]

    return parts

def generate_auth_token():
    """Generate a secure authentication token"""
    # Generate a random 32-byte token
    token = secrets.token_hex(32)
    # Add timestamp for expiration checking
    timestamp = str(int(time.time()))
    # Create a hash of token + timestamp for verification
    token_hash = hashlib.sha256((token + timestamp).encode()).hexdigest()
    return f"{token}:{timestamp}:{token_hash}"

def send_token_to_server(token):
    """Send the authentication token to the game server"""
    try:
        # Your game server token validation endpoint
        token_url = "https://aion-reclaim.com/Game-Launcher/validate_token.php"

        # Get client's public IP
        try:
            ip_response = requests.get('https://api.ipify.org', timeout=5)
            client_ip = ip_response.text.strip()
            print(f"Client IP: {client_ip}")
        except:
            client_ip = "unknown"
            print("Could not determine client IP")

        token_data = {
            'token': token,
            'action': 'register',
            'client_ip': client_ip
        }

        response = requests.post(token_url, json=token_data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            return result.get('success', False)
        else:
            print(f"Token registration failed: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"Failed to register token with server: {e}")
        return False

def send_launcher_auth_packet(token, server_ip="************", server_port=2106):
    """Send CM_LAUNCHER_AUTH packet directly to game server"""
    try:
        print(f"Connecting to game server {server_ip}:{server_port}...")

        # Create socket connection to game server
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10 second timeout
        sock.connect((server_ip, server_port))

        print("Connected to game server, sending authentication packet...")

        # Create CM_LAUNCHER_AUTH packet
        # Packet format: [opcode][token_string][null_terminator]
        opcode = 0xDD  # For version 4.11, adjust based on your server version
        token_bytes = token.encode('utf-8')

        packet = bytearray()
        packet.append(opcode)
        packet.extend(token_bytes)
        packet.append(0x00)  # Null terminator

        # Send the packet
        sock.send(packet)
        print(f"Authentication packet sent successfully (token: {token[:20]}...)")

        # Close the connection
        sock.close()
        return True

    except Exception as e:
        print(f"Failed to send authentication packet: {e}")
        return False

# Determine base directory
if getattr(sys, 'frozen', False):
    script_dir = os.path.dirname(sys.executable)
else:
    script_dir = os.path.dirname(os.path.abspath(__file__))

class SplashScreen:
    def __init__(self):
        self.splash = tk.Toplevel()
        self.splash.overrideredirect(True)
        self.splash.attributes('-alpha', 0.0)
        
        # Get screen dimensions
        screen_width = self.splash.winfo_screenwidth()
        screen_height = self.splash.winfo_screenheight()
        
        # Set splash window size
        splash_width = 400
        splash_height = 300
        
        # Calculate position for center of screen
        x = (screen_width - splash_width) // 2
        y = (screen_height - splash_height) // 2
        
        self.splash.geometry(f'{splash_width}x{splash_height}+{x}+{y}')
        
        # Create main frame
        self.frame = tk.Frame(self.splash, bg='#1f2126')
        self.frame.pack(fill='both', expand=True)
        
        # Load and display logo
        try:
            logo_path = os.path.join(script_dir, "logo.png")
            if not os.path.exists(logo_path):
                # Download logo if not exists
                try:
                    logo_url = "https://aion-blitz.online/aion-blitz/Game-Launcher/Images/logo.png"
                    urllib.request.urlretrieve(logo_url, logo_path)
                except Exception as download_error:
                    print(f"Could not download logo: {download_error}")
                    # Create a placeholder logo file or skip logo loading
                    return

            logo_img = Image.open(logo_path)
            logo_img = logo_img.resize((200, 200), Image.Resampling.LANCZOS)
            self.logo_photo = ImageTk.PhotoImage(logo_img)
            self.logo_label = tk.Label(self.frame, image=self.logo_photo, bg='#1f2126')
            self.logo_label.pack(pady=20)
        except Exception as e:
            print(f"Error loading logo: {e}")
        
        # Loading text
        self.loading_label = tk.Label(self.frame, text="Loading...", font=("Arial", 12), bg='#1f2126', fg='white')
        self.loading_label.pack(pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(self.frame, length=300, mode='determinate', style="Splash.Horizontal.TProgressbar")
        self.progress.pack(pady=10)
        
        # Configure progress bar style
        style = ttk.Style()
        style.configure("Splash.Horizontal.TProgressbar", 
                       troughcolor='#1f2126',
                       background='#ff8800',
                       thickness=10)
        
        # Start fade in animation
        self.fade_in()
        
    def fade_in(self):
        alpha = self.splash.attributes('-alpha')
        if alpha < 1.0:
            alpha += 0.1
            self.splash.attributes('-alpha', alpha)
            self.splash.after(20, self.fade_in)
        else:
            self.animate_progress()
    
    def animate_progress(self):
        value = self.progress['value']
        if value < 100:
            value += 2
            self.progress['value'] = value
            self.splash.after(20, self.animate_progress)
        else:
            self.fade_out()
    
    def fade_out(self):
        alpha = self.splash.attributes('-alpha')
        if alpha > 0:
            alpha -= 0.1
            self.splash.attributes('-alpha', alpha)
            self.splash.after(20, self.fade_out)
        else:
            self.splash.destroy()

class ModernLauncher:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("Aion-Blitz Launcher 4.8")
        self.window.configure(bg="#0a0a0a")
        self.window.resizable(False, False)

        # Remove window decorations for modern look
        self.window.overrideredirect(True)

        # Image cache to avoid downloading same image multiple times
        self.image_cache = {}

        # Show splash screen
        self.splash = SplashScreen()

        # Configure window size and position (larger for more content)
        window_width, window_height = 1200, 800
        screen_width, screen_height = self.window.winfo_screenwidth(), self.window.winfo_screenheight()
        x, y = (screen_width // 2) - (window_width // 2), (screen_height // 2) - (window_height // 2)
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # Add custom title bar
        self.create_title_bar()

        # Create main content area
        self.create_main_content()

        # Configure styles
        self.configure_styles()

        # Initialize status and check for updates
        self.window.after(500, lambda: self.update_status("Ready to launch"))
        self.window.after(1500, self.auto_check_updates)  # Check for updates after launcher loads (increased delay)

        # Make window draggable
        self.make_draggable()
        
    def create_title_bar(self):
        # Custom title bar with gaming aesthetic
        self.title_bar = tk.Frame(self.window, bg="#1a1a1a", height=50)
        self.title_bar.pack(fill="x", side="top")
        self.title_bar.pack_propagate(False)

        # Left side - Navigation buttons
        nav_frame = tk.Frame(self.title_bar, bg="#1a1a1a")
        nav_frame.pack(side="left", padx=10, pady=5)

        # Website button
        website_nav = tk.Label(nav_frame, text="🌐 Website", font=("Segoe UI", 10, "bold"),
                              fg="#00d4ff", bg="#1a1a1a", cursor="hand2", padx=10, pady=5)
        website_nav.pack(side="left", padx=5)
        website_nav.bind("<Button-1>", lambda e: self.open_website())
        website_nav.bind("<Enter>", lambda e: website_nav.config(bg="#333333"))
        website_nav.bind("<Leave>", lambda e: website_nav.config(bg="#1a1a1a"))

        # Shop button
        shop_nav = tk.Label(nav_frame, text="🛒 Shop", font=("Segoe UI", 10, "bold"),
                           fg="#ff8800", bg="#1a1a1a", cursor="hand2", padx=10, pady=5)
        shop_nav.pack(side="left", padx=5)
        shop_nav.bind("<Enter>", lambda e: shop_nav.config(bg="#333333"))
        shop_nav.bind("<Leave>", lambda e: shop_nav.config(bg="#1a1a1a"))

        # Discord button
        discord_nav = tk.Label(nav_frame, text="💬 Discord", font=("Segoe UI", 10, "bold"),
                              fg="#7289da", bg="#1a1a1a", cursor="hand2", padx=10, pady=5)
        discord_nav.pack(side="left", padx=5)
        discord_nav.bind("<Enter>", lambda e: discord_nav.config(bg="#333333"))
        discord_nav.bind("<Leave>", lambda e: discord_nav.config(bg="#1a1a1a"))

        # Center - Title
        title_label = tk.Label(self.title_bar, text="🗡️ Aion Blitz Launcher 4.8",
                              font=("Segoe UI", 14, "bold"), fg="#ffd700", bg="#1a1a1a")
        title_label.pack(side="left", expand=True)

        # Right side - Server status and controls
        right_frame = tk.Frame(self.title_bar, bg="#1a1a1a")
        right_frame.pack(side="right", padx=10, pady=5)

        # Server status indicators
        login_status = tk.Label(right_frame, text="Login Server 🟢", font=("Segoe UI", 9),
                               fg="#00ff00", bg="#1a1a1a", padx=5)
        login_status.pack(side="left", padx=5)

        game_status = tk.Label(right_frame, text="Game Server 🟢", font=("Segoe UI", 9),
                              fg="#00ff00", bg="#1a1a1a", padx=5)
        game_status.pack(side="left", padx=5)

        # Minimize button
        min_btn = tk.Label(right_frame, text="−", font=("Segoe UI", 16, "bold"),
                          fg="#ffffff", bg="#1a1a1a", cursor="hand2", padx=15, pady=5)
        min_btn.pack(side="right", padx=2)
        min_btn.bind("<Button-1>", lambda e: self.window.iconify())
        min_btn.bind("<Enter>", lambda e: min_btn.config(bg="#444444"))
        min_btn.bind("<Leave>", lambda e: min_btn.config(bg="#1a1a1a"))

        # Close button
        close_btn = tk.Label(right_frame, text="✕", font=("Segoe UI", 14, "bold"),
                            fg="#ffffff", bg="#1a1a1a", cursor="hand2", padx=15, pady=5)
        close_btn.pack(side="right", padx=2)
        close_btn.bind("<Button-1>", lambda e: self.window.quit())
        close_btn.bind("<Enter>", lambda e: close_btn.config(bg="#ff4444"))
        close_btn.bind("<Leave>", lambda e: close_btn.config(bg="#1a1a1a"))
    
    def create_main_content(self):
        # Main content frame (no background to show image behind)
        self.main_frame = tk.Frame(self.window)
        self.main_frame.pack(fill="both", expand=True)

        # Load background after main frame is created
        self.load_background()

        # Create the beautiful layout inspired by the reference
        self.create_game_logo_section()
        self.create_news_section()
        self.create_action_buttons_section()
        self.create_game_client_status()
        self.create_bottom_status_bar()

    def load_background(self):
        background_image_path = os.path.join(script_dir, "background_image.png")

        if not os.path.exists(background_image_path):
            # Download background image
            try:
                background_url = "https://aion-blitz.online/aion-blitz/Game-Launcher/Images/background_image.png"
                urllib.request.urlretrieve(background_url, background_image_path)
            except Exception as e:
                pass  # Silently fail and use gradient fallback

        if os.path.exists(background_image_path):
            try:
                background_image = Image.open(background_image_path)
                background_image = background_image.resize((1200, 750), Image.Resampling.LANCZOS)  # Adjust for main frame size
                self.background_photo = ImageTk.PhotoImage(background_image)
                self.background_label = tk.Label(self.main_frame, image=self.background_photo, borderwidth=0)
                self.background_label.place(x=0, y=0, width=1200, height=750)  # Fill main frame
                # Send background to back
                self.background_label.lower()
            except Exception as e:
                # Create a dark gradient background as fallback
                self.create_gradient_background()
        else:
            self.create_gradient_background()
    
    def create_gradient_background(self):
        # Create a beautiful fantasy-themed gradient as fallback
        self.bg_canvas = tk.Canvas(self.main_frame, width=1200, height=750, highlightthickness=0)
        self.bg_canvas.place(x=0, y=0)  # Fill main frame

        # Create a golden/amber gradient inspired by fantasy themes
        for i in range(750):
            # Create a gradient from dark amber to golden
            progress = i / 750
            r = int(20 + progress * 100)  # Red component
            g = int(15 + progress * 80)   # Green component
            b = int(5 + progress * 20)    # Blue component (keep low for amber effect)
            color = f"#{r:02x}{g:02x}{b:02x}"
            self.bg_canvas.create_line(0, i, 1200, i, fill=color, width=1)

        # Send gradient to back
        self.bg_canvas.lower()

    def create_game_logo_section(self):
        # Draw logo text directly on the background canvas for true transparency
        # This way the text becomes part of the background image itself

        # Check if we have a background canvas (either image or gradient)
        if hasattr(self, 'bg_canvas'):
            # Draw text directly on the background canvas - truly transparent!
            # Game title "AION" on first line
            self.bg_canvas.create_text(175, 100, text="AION",
                                      font=("Arial", 48, "bold"),
                                      fill="#ffd700", anchor="center")

            # Game title "BLITZ" on second line
            self.bg_canvas.create_text(175, 150, text="BLITZ",
                                      font=("Arial", 48, "bold"),
                                      fill="#ffd700", anchor="center")

            # Version number
            self.bg_canvas.create_text(175, 200, text="4.8",
                                      font=("Segoe UI", 24, "bold"),
                                      fill="#ffaa00", anchor="center")

            # Decorative line under version
            self.bg_canvas.create_line(125, 220, 225, 220, fill="#ffd700", width=3)
    
    def create_news_section(self):
        # News section inspired by the reference image
        news_frame = tk.Frame(self.main_frame, bg="#0a0a0a", highlightthickness=0)
        news_frame.place(x=50, y=300, width=700, height=300)

        # News header
        news_header = tk.Canvas(news_frame, width=700, height=50, highlightthickness=0, bg="#0a0a0a")
        news_header.pack(fill="x")

        # Semi-transparent background for news header
        news_header.create_rectangle(0, 0, 700, 50, fill="#1a1a2e", stipple="gray50", outline="#4a4a6a", width=2)

        # News title
        news_title = tk.Label(news_header, text="NEWS", font=("Segoe UI", 16, "bold"),
                             fg="#7289da", bg="#1a1a2e", bd=0)
        news_header.create_window(50, 25, window=news_title)

        # More news button
        more_news_btn = tk.Label(news_header, text="+ MORE NEWS >", font=("Segoe UI", 12, "bold"),
                                fg="#00d4ff", bg="#1a1a2e", cursor="hand2", bd=0)
        news_header.create_window(600, 25, window=more_news_btn)
        more_news_btn.bind("<Enter>", lambda e: more_news_btn.config(fg="#ffffff"))
        more_news_btn.bind("<Leave>", lambda e: more_news_btn.config(fg="#00d4ff"))

        # News cards container
        cards_frame = tk.Frame(news_frame, bg="#0a0a0a", highlightthickness=0)
        cards_frame.pack(fill="both", expand=True, pady=10)

        # Load and create news cards dynamically from server
        self.load_and_create_news_cards(cards_frame)

    def load_and_create_news_cards(self, cards_frame):
        """Load news from server and create cards dynamically"""
        try:
            # First try to load from local file for testing
            local_news_file = "example_news_with_images.json"
            if os.path.exists(local_news_file):
                with open(local_news_file, 'r') as f:
                    news_data = json.load(f)
                    # Create cards for each news item (max 3 for layout)
                    for i, news_item in enumerate(news_data.get('news', [])[:3]):
                        self.create_news_card(cards_frame, i,
                                            news_item.get('title', 'News Title'),
                                            news_item.get('subtitle', 'News Subtitle'),
                                            news_item.get('date', 'Date'),
                                            news_item.get('content', ''),
                                            news_item.get('image', ''))
                return

            # Try to fetch news from your server
            print("Loading news from server...")
            news_url = "https://aion-blitz.online/aion-blitz/Game-Launcher/news/news.json"
            response = requests.get(news_url, timeout=5)

            if response.status_code == 200:
                news_data = response.json()
                # Create cards for each news item (max 3 for layout)
                for i, news_item in enumerate(news_data.get('news', [])[:3]):
                    self.create_news_card(cards_frame, i,
                                        news_item.get('title', 'News Title'),
                                        news_item.get('subtitle', 'News Subtitle'),
                                        news_item.get('date', 'Date'),
                                        news_item.get('content', ''),
                                        news_item.get('image', ''))
            else:
                print(f"Server returned status code: {response.status_code}")
                # Fallback to default news if server is unreachable
                self.create_default_news_cards(cards_frame)

        except Exception as e:
            print(f"Failed to load news: {e}")
            # Fallback to default news
            self.create_default_news_cards(cards_frame)

    def create_default_news_cards(self, cards_frame):
        """Create default news cards when server is unreachable"""
        self.create_news_card(cards_frame, 0, "The eternal challenge", "Custom Instance", "6/21/2025", "", "")
        self.create_news_card(cards_frame, 1, "Bounty Hunting System", "Every kill count", "6/21/2025", "", "")
        self.create_news_card(cards_frame, 2, "PvP Map", "Command .pvp", "6/21/2025", "", "")

    def create_news_card(self, parent, index, title, subtitle, date, content="", image_url=""):
        # Individual news card
        card_canvas = tk.Canvas(parent, width=220, height=200, highlightthickness=0, bg="#0a0a0a")
        card_canvas.place(x=index * 230, y=0)

        # Card background with fantasy styling
        card_canvas.create_rectangle(5, 5, 215, 195, fill="#2a2a3e", outline="#4a4a6a", width=2)

        # Image area - load from URL if provided, otherwise use placeholder
        card_canvas.create_rectangle(15, 15, 205, 120, fill="#8b6914", outline="#ffd700", width=2)

        if image_url:
            # Load image from URL
            self.load_news_image(card_canvas, image_url, 110, 67)
        else:
            # Fallback to castle icon
            card_canvas.create_text(110, 67, text="🏰", font=("Segoe UI", 24), fill="#ffd700")

        # Info section background
        card_canvas.create_rectangle(15, 130, 205, 185, fill="#1a1a2e", outline="#4a4a6a", width=1)

        # Info label
        info_label = tk.Label(card_canvas, text="INFO", font=("Segoe UI", 10, "bold"),
                             fg="#7289da", bg="#1a1a2e", bd=0)
        card_canvas.create_window(35, 145, window=info_label)

        # Title
        title_label = tk.Label(card_canvas, text=title, font=("Segoe UI", 11, "bold"),
                              fg="#ffffff", bg="#1a1a2e", bd=0, wraplength=180)
        card_canvas.create_window(110, 160, window=title_label)

        # Subtitle and date
        subtitle_label = tk.Label(card_canvas, text=f"{subtitle} | {date}", font=("Segoe UI", 9),
                                 fg="#888888", bg="#1a1a2e", bd=0)
        card_canvas.create_window(110, 175, window=subtitle_label)

        # Make card clickable with hover effect
        card_canvas.bind("<Enter>", lambda e: card_canvas.config(cursor="hand2"))
        card_canvas.bind("<Leave>", lambda e: card_canvas.config(cursor=""))
        card_canvas.bind("<Button-1>", lambda e: self.show_news_detail(title, subtitle, date, content))

    def load_news_image(self, canvas, image_url, x, y):
        """Load and display image from URL in the news card with caching"""

        # Check if image is already cached
        if image_url in self.image_cache:
            photo = self.image_cache[image_url]
            canvas.create_image(x, y, image=photo)
            # Keep a reference to prevent garbage collection
            if not hasattr(canvas, 'images'):
                canvas.images = []
            canvas.images.append(photo)
            return

        def load_image_thread():
            try:
                print(f"Downloading image from: {image_url}")
                # Add headers to avoid being blocked
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                # Download image from URL with retry logic
                response = requests.get(image_url, timeout=10, headers=headers)
                print(f"Response status: {response.status_code}")

                if response.status_code == 200:
                    # Save image temporarily
                    temp_image_path = f"temp_news_image_{abs(hash(image_url))}.jpg"
                    with open(temp_image_path, 'wb') as f:
                        f.write(response.content)
                    print(f"Image saved to: {temp_image_path}")

                    # Load and resize image to fit the card
                    image = Image.open(temp_image_path)
                    print(f"Original image size: {image.size}")
                    # Resize to fit the news card area (190x105 to fit in the rectangle)
                    image = image.resize((190, 105), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(image)
                    print("Image converted to PhotoImage")

                    # Cache the image for reuse
                    self.image_cache[image_url] = photo

                    # Display image on canvas (must be done in main thread)
                    def display_image():
                        print(f"Displaying image at position ({x}, {y})")
                        canvas.create_image(x, y, image=photo)
                        # Keep a reference to prevent garbage collection
                        if not hasattr(canvas, 'images'):
                            canvas.images = []
                        canvas.images.append(photo)
                        print("Image displayed successfully")

                    # Schedule display in main thread
                    canvas.after(0, display_image)

                    # Clean up temp file
                    try:
                        os.remove(temp_image_path)
                    except:
                        pass
                elif response.status_code == 429:
                    print("Rate limited by server, retrying in 2 seconds...")
                    time.sleep(2)
                    # Retry once
                    response = requests.get(image_url, timeout=10, headers=headers)
                    if response.status_code == 200:
                        # Same processing as above
                        temp_image_path = f"temp_news_image_{abs(hash(image_url))}.jpg"
                        with open(temp_image_path, 'wb') as f:
                            f.write(response.content)
                        image = Image.open(temp_image_path)
                        image = image.resize((190, 105), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(image)
                        self.image_cache[image_url] = photo
                        def display_image():
                            canvas.create_image(x, y, image=photo)
                            if not hasattr(canvas, 'images'):
                                canvas.images = []
                            canvas.images.append(photo)
                        canvas.after(0, display_image)
                        try:
                            os.remove(temp_image_path)
                        except:
                            pass
                    else:
                        print(f"Retry failed: HTTP {response.status_code}")
                        def show_fallback():
                            canvas.create_text(x, y, text="🏰", font=("Segoe UI", 24), fill="#ffd700")
                        canvas.after(0, show_fallback)
                else:
                    print(f"Failed to download image: HTTP {response.status_code}")
                    # Fallback to castle icon
                    def show_fallback():
                        canvas.create_text(x, y, text="🏰", font=("Segoe UI", 24), fill="#ffd700")
                    canvas.after(0, show_fallback)

            except Exception as e:
                print(f"Failed to load news image from {image_url}: {e}")
                # Fallback to castle icon if image fails to load
                def show_fallback():
                    canvas.create_text(x, y, text="🏰", font=("Segoe UI", 24), fill="#ffd700")
                canvas.after(0, show_fallback)

        # Add a small delay to avoid hitting rate limits
        def delayed_load():
            threading.Thread(target=load_image_thread, daemon=True).start()

        # Stagger the image loading to avoid rate limits
        delay = hash(image_url) % 3000  # Random delay between 0-3 seconds
        canvas.after(abs(delay), delayed_load)

    def create_action_buttons_section(self):
        # Action buttons section on the right side - SINGLE PLAY BUTTON ONLY
        buttons_frame = tk.Frame(self.main_frame, bg="#0a0a0a", highlightthickness=0)
        buttons_frame.place(x=800, y=350, width=350, height=200)

        # Single Play button (cyan like in your reference image)
        play_canvas = tk.Canvas(buttons_frame, width=320, height=60, highlightthickness=0, bg="#0a0a0a")
        play_canvas.pack(pady=20)
        play_canvas.create_rectangle(5, 5, 315, 55, fill="#00bcd4", outline="#00acc1", width=2)
        play_canvas.create_text(160, 30, text="▶ PLAY", font=("Segoe UI", 16, "bold"), fill="#ffffff")
        play_canvas.bind("<Button-1>", lambda e: self.play_game())
        play_canvas.bind("<Enter>", lambda e: self.button_hover_enter(play_canvas, "#00acc1"))
        play_canvas.bind("<Leave>", lambda e: self.button_hover_leave(play_canvas, "#00bcd4"))

    def create_game_client_status(self):
        # Game client status section (like in reference)
        status_frame = tk.Frame(self.main_frame, bg="#0a0a0a", highlightthickness=0)
        status_frame.place(x=800, y=450, width=350, height=150)

        # Status panel background
        status_canvas = tk.Canvas(status_frame, width=320, height=140, highlightthickness=0, bg="#0a0a0a")
        status_canvas.pack()
        status_canvas.create_rectangle(5, 5, 315, 135, fill="#1a1a2e", outline="#4a4a6a", width=2)

        # Game client header
        client_header = tk.Label(status_canvas, text="⚙ GAME CLIENT", font=("Segoe UI", 12, "bold"),
                                fg="#ffffff", bg="#1a1a2e", bd=0)
        status_canvas.create_window(80, 25, window=client_header)

        # Up to date indicator
        uptodate_label = tk.Label(status_canvas, text="🟢 UP TO DATE", font=("Segoe UI", 10, "bold"),
                                 fg="#00ff00", bg="#1a1a2e", bd=0)
        status_canvas.create_window(250, 25, window=uptodate_label)

        # Files verified
        verified_label = tk.Label(status_canvas, text="All files verified", font=("Segoe UI", 10),
                                 fg="#ffffff", bg="#1a1a2e", bd=0)
        status_canvas.create_window(100, 55, window=verified_label)

        # Percentage
        percent_label = tk.Label(status_canvas, text="100%", font=("Segoe UI", 12, "bold"),
                                fg="#00ff00", bg="#1a1a2e", bd=0)
        status_canvas.create_window(270, 55, window=percent_label)

        # Client version
        version_info = tk.Label(status_canvas, text="Client version: 8.4 | Last checked: Today at 21:02",
                               font=("Segoe UI", 9), fg="#888888", bg="#1a1a2e", bd=0)
        status_canvas.create_window(160, 85, window=version_info)

        # Launcher version
        launcher_version = tk.Label(status_canvas, text="🚀 Launcher Version 1.0 ✏", font=("Segoe UI", 10, "bold"),
                                   fg="#ffd700", bg="#1a1a2e", bd=0)
        status_canvas.create_window(160, 110, window=launcher_version)



    def on_button_enter(self, button, hover_color):
        button.config(bg=hover_color)

    def on_button_leave(self, button, original_color):
        button.config(bg=original_color)

    def button_hover_enter(self, canvas, hover_color):
        canvas.delete("all")
        canvas.create_rectangle(5, 5, 315, 55, fill=hover_color, outline=hover_color, width=2)
        # Only PLAY button now
        canvas.create_text(160, 30, text="▶ PLAY", font=("Segoe UI", 16, "bold"), fill="#ffffff")

    def button_hover_leave(self, canvas, original_color):
        canvas.delete("all")
        canvas.create_rectangle(5, 5, 315, 55, fill=original_color, outline=original_color, width=2)
        # Only PLAY button now
        canvas.create_text(160, 30, text="▶ PLAY", font=("Segoe UI", 16, "bold"), fill="#ffffff")

    def create_bottom_status_bar(self):
        # Bottom status bar with modern styling
        self.status_frame = tk.Frame(self.main_frame, height=40)
        self.status_frame.pack(side="bottom", fill="x", pady=(20, 10))

        # Create canvas for status bar background
        status_canvas = tk.Canvas(self.status_frame, height=40, highlightthickness=0, bg="#0a0a0a")
        status_canvas.pack(fill="x")

        # Draw semi-transparent background with border
        status_canvas.create_rectangle(0, 0, 1200, 40, fill="#1a1a2e", stipple="gray25", outline="#4a4a6a", width=2)

        # Status message
        self.status_label = tk.Label(status_canvas, text="Ready to launch",
                                    font=("Segoe UI", 11, "bold"),
                                    fg="#00d4ff", bg="#1a1a2e", anchor="w")
        status_canvas.create_window(20, 20, window=self.status_label, anchor="w")

        # Last check info (like in reference)
        last_check_label = tk.Label(status_canvas, text="Last check: 9:40:04 AM",
                                   font=("Segoe UI", 10),
                                   fg="#888888", bg="#1a1a2e", anchor="e")
        status_canvas.create_window(1180, 20, window=last_check_label, anchor="e")

    def make_draggable(self):
        # Make window draggable by title bar
        def start_move(event):
            self.window.x = event.x
            self.window.y = event.y

        def stop_move(event):
            self.window.x = None
            self.window.y = None

        def do_move(event):
            if hasattr(self.window, 'x') and self.window.x is not None:
                deltax = event.x - self.window.x
                deltay = event.y - self.window.y
                x = self.window.winfo_x() + deltax
                y = self.window.winfo_y() + deltay
                self.window.geometry(f"+{x}+{y}")

        # Bind to title bar for dragging
        self.title_bar.bind("<Button-1>", start_move)
        self.title_bar.bind("<ButtonRelease-1>", stop_move)
        self.title_bar.bind("<B1-Motion>", do_move)

    def show_news_detail(self, title, subtitle, date, content=""):
        """Show news detail in a popup"""
        detail_text = f"Title: {title}\n\nDescription: {subtitle}\n\nDate: {date}"
        if content:
            detail_text += f"\n\nContent:\n{content}"
        else:
            detail_text += "\n\nThis feature would show detailed news content in a full launcher."
        messagebox.showinfo("News Detail", detail_text)

    def configure_styles(self):
        style = ttk.Style()
        style.configure("Modern.Horizontal.TProgressbar",
                       troughcolor="#1f2126",
                       background="#ff8800",
                       thickness=10)
    
    def check_for_cheats_before_launch(self):
        """Check for cheating software before allowing game launch"""
        blacklisted_processes = [process.lower().strip() for process in cheat_tools if process.strip()]
        detected_cheats = []

        try:
            for p in psutil.process_iter(['name']):
                try:
                    process_name = p.name().lower()
                    for blacklisted in blacklisted_processes:
                        if blacklisted in process_name:
                            detected_cheats.append(process_name)
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"Pre-launch cheat check error: {e}")

        if detected_cheats:
            cheat_list = ', '.join(set(detected_cheats))
            messagebox.showerror("Anti-Cheat Block",
                               f"Detected cheating software: {cheat_list}\n\n"
                               f"Please close all cheating tools before launching the game.")
            return False

        return True

    def play_game(self):
        # Check for cheating software before launch
        if not self.check_for_cheats_before_launch():
            self.update_status("Launch blocked - cheating software detected")
            return

        # Generate authentication token
        auth_token = generate_auth_token()
        print(f"Generated auth token: {auth_token[:20]}...")  # Only show first 20 chars for security

        # Register token with web server (existing system)
        self.update_status("Registering authentication token...")
        if not send_token_to_server(auth_token):
            messagebox.showerror("Authentication Failed",
                               "Failed to register token with server. Please try again.")
            self.update_status("Authentication failed")
            return

        # Launch game with token parameter
        self.update_status("Launching game with authentication...")
        self.window.withdraw()
        process_monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        process_monitor_thread.start()

        # Create authentication file for the client to read
        auth_file_path = os.path.join(script_dir, "launcher_auth.tmp")
        try:
            with open(auth_file_path, 'w') as f:
                f.write(auth_token)
            print(f"Created auth file: {auth_file_path}")
        except Exception as e:
            print(f"Failed to create auth file: {e}")
            messagebox.showerror("Error", "Failed to create authentication file")
            return

        game_path = os.path.join(script_dir, "bin64", "AION.bin")
        # Launch game without token parameter (it will read from auth file)
        game_command = f'start /affinity 7FFFFFFF "" "{game_path}" -ip:************ -port:2126 -cc:2 -lang:ENG --noauthgg -noweb -nobs -webshopevent:0 -st -ingamebrowser -charnamemenu -loginex -nowebshop -nokicks -ncg -noauthgg -ls -charnamemenu -ingameshop -DEVMODE "con_disable_console 0" -DEVMODE "g_chatlog 1" -DEVMODE "g_freefly 1" -DEVMODE "g_auto_disconnect 0" -win10-mouse-fix -fix-stack-win11'

        subprocess.run(game_command, shell=True)

        # Clean up auth file after a delay (client should have read it by then)
        def cleanup_auth_file():
            time.sleep(10)  # Wait 10 seconds
            try:
                if os.path.exists(auth_file_path):
                    os.remove(auth_file_path)
                    print("Cleaned up auth file")
            except Exception as e:
                print(f"Failed to cleanup auth file: {e}")

        cleanup_thread = threading.Thread(target=cleanup_auth_file, daemon=True)
        cleanup_thread.start()
    
    def open_website(self):
        webbrowser.open("https://aion-blitz.online")
    
    def monitor_processes(self):
        blacklisted_processes = [process.lower().strip() for process in cheat_tools if process.strip()]
        while True:
            try:
                running_processes = []
                for p in psutil.process_iter(['name']):
                    try:
                        running_processes.append(p.name().lower())
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                detected_cheats = []

                # Check for suspicious processes using "contains" matching
                for process in running_processes:
                    for blacklisted in blacklisted_processes:
                        if blacklisted in process:  # CONTAINS matching instead of exact
                            detected_cheats.append(process)
                            break

                if detected_cheats:
                    cheat_list = ', '.join(set(detected_cheats))
                    messagebox.showwarning("Anti-Cheat Alert",
                                         f"Cheating software detected: {cheat_list}\n"
                                         f"The game will be terminated.")

                    # Terminate detected cheat processes
                    for p in psutil.process_iter(['name']):
                        try:
                            process_name = p.name().lower()
                            if process_name in detected_cheats:
                                p.terminate()
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            continue

                    self.terminate_game()
                    if self.window:
                        self.window.deiconify()
                    break

            except Exception as e:
                print(f"Anti-cheat monitoring error: {e}")

            time.sleep(1)
    
    def terminate_game(self):
        for process in psutil.process_iter(['name']):
            if process.name().lower() == "aion.bin":
                process.terminate()
                break
    
    def update_status(self, message):
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)

    def auto_check_updates(self):
        """Automatically check and download updates when launcher starts"""
        try:
            self.update_status("Checking for updates...")
            patch_files = get_patch_files("https://aion-blitz.online/aion-blitz/Game-Launcher/Patches/")

            if not patch_files:
                self.update_status("Ready to launch")
                return

            current_version, processed_files = get_current_version()
            filtered_patch_files = []

            if current_version is None or current_version.strip() == "":
                # No version file exists or is empty, download all patches in natural order
                filtered_patch_files = sorted(patch_files, key=natural_sort_key)
            else:
                # Check for newer patches and unprocessed non-versioned files
                for patch_file in patch_files:
                    try:
                        version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                        if version_match:
                            # Versioned patch - check if newer than current version
                            patch_version = version_match.group(1)
                            if current_version == "updated" or version.parse(patch_version) > version.parse(current_version):
                                filtered_patch_files.append(patch_file)
                        else:
                            # Non-versioned patch - check if not already processed
                            if patch_file not in processed_files:
                                filtered_patch_files.append(patch_file)
                    except (AttributeError, ValueError):
                        # If we can't parse version, check if not already processed
                        if patch_file not in processed_files:
                            filtered_patch_files.append(patch_file)

                # Sort to ensure consistent natural order
                filtered_patch_files = sorted(filtered_patch_files, key=natural_sort_key)

            if filtered_patch_files:
                # Download and install patches with progress
                self.download_patches_with_progress(filtered_patch_files)
            else:
                self.update_status("Game is up to date - Ready to launch")

        except Exception as e:
            print(f"Auto update check failed: {e}")
            self.update_status("Ready to launch")

    def download_patches_with_progress(self, filtered_patch_files):
        """Download and install patches with visible progress"""
        # Show immediate status update
        self.update_status("Preparing to download patches...")

        # Create progress frame immediately
        progress_frame = tk.Frame(self.main_frame, bg="#1a1a2e")
        progress_frame.place(relx=0.5, rely=0.8, anchor="center")

        progress_bar = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL,
                                     length=400, mode='determinate',
                                     style="Modern.Horizontal.TProgressbar")
        progress_bar.pack(pady=10)

        progress_label = tk.Label(progress_frame, text="Initializing...",
                                font=("Segoe UI", 12), bg="#1a1a2e", fg="#ffffff")
        progress_label.pack(pady=5)

        # Force UI update to show progress bar immediately
        progress_frame.update()
        self.window.update()

        def download_progress(block_count, block_size, total_size):
            if total_size > 0:
                downloaded = block_count * block_size
                progress_percentage = int((downloaded / total_size) * 100)
                progress_bar['value'] = progress_percentage
                progress_label['text'] = f"{progress_percentage}%"
                progress_frame.update()

        def download_thread():
            try:
                # Get current version info
                current_version, processed_files = get_current_version()
                latest_version = current_version if current_version and current_version != "updated" else None
                new_processed_files = processed_files.copy()

                total_patches = len(filtered_patch_files)
                for i, patch_file in enumerate(filtered_patch_files, 1):
                    # Update status and UI immediately
                    def update_download_status():
                        self.update_status(f"Downloading {patch_file} ({i}/{total_patches})...")
                        progress_label['text'] = f"Downloading {patch_file}..."
                        progress_frame.update()
                        self.window.update()

                    # Schedule UI update in main thread
                    self.window.after(0, update_download_status)

                    patch_url = "https://aion-blitz.online/aion-blitz/Game-Launcher/Patches/" + patch_file
                    patch_path = os.path.join(script_dir, patch_file)

                    # Download patch with progress
                    req = urllib.request.Request(patch_url, headers={"User-Agent": "MyGameLauncher"})
                    with urllib.request.urlopen(req, timeout=30) as response, open(patch_path, 'wb') as out_file:
                        total_size = int(response.info().get('Content-Length', 0))
                        block_size = 8192
                        block_count = 0
                        while True:
                            buffer = response.read(block_size)
                            if not buffer:
                                break
                            out_file.write(buffer)
                            block_count += 1
                            download_progress(block_count, block_size, total_size)

                            # Update UI every 50 blocks to keep it responsive
                            if block_count % 50 == 0:
                                self.window.update()

                    # Extract patch
                    def update_extract_status():
                        self.update_status(f"Installing {patch_file} ({i}/{total_patches})...")
                        progress_label['text'] = "Extracting..."
                        progress_bar['value'] = 0  # Reset progress bar for extraction
                        progress_frame.update()
                        self.window.update()

                    # Schedule UI update in main thread
                    self.window.after(0, update_extract_status)

                    with zipfile.ZipFile(patch_path, 'r') as zip_ref:
                        zip_ref.extractall(script_dir)

                    # Update progress to show extraction complete
                    def update_extract_complete():
                        progress_bar['value'] = 100
                        progress_label['text'] = "Extraction complete"
                        progress_frame.update()
                        self.window.update()

                    self.window.after(0, update_extract_complete)

                    # Update version tracking
                    version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                    if version_match:
                        # Versioned patch - update the version number
                        latest_version = version_match.group(1)
                    else:
                        # Non-versioned patch - add to processed files list
                        if patch_file not in new_processed_files:
                            new_processed_files.append(patch_file)

                    # Clean up
                    os.remove(patch_path)

                    # Reset progress for next file
                    progress_bar['value'] = 0
                    progress_label['text'] = "0%"

                # Save final version state
                final_version = latest_version if latest_version else "updated"
                save_version(final_version, new_processed_files)

                progress_frame.destroy()
                self.update_status("Updates completed - Ready to launch")

            except Exception as e:
                print(f"Patch download failed: {e}")
                progress_frame.destroy()
                self.update_status("Update failed - Ready to launch")

        # Add a small delay to ensure progress bar is visible, then start download
        def start_download_after_delay():
            progress_label['text'] = "Starting download..."
            progress_frame.update()
            # Small delay to let UI update
            self.window.after(500, lambda: threading.Thread(target=download_thread, daemon=True).start())

        # Start the delayed download
        start_download_after_delay()

    def check_updates(self):
        # Update status
        self.update_status("Checking for updates...")

        # Create progress frame
        progress_frame = tk.Frame(self.main_frame, bg="#1a1a2e")
        progress_frame.place(relx=0.5, rely=0.8, anchor="center")

        progress_bar = ttk.Progressbar(progress_frame, orient=tk.HORIZONTAL,
                                     length=400, mode='determinate',
                                     style="Modern.Horizontal.TProgressbar")
        progress_bar.pack(pady=10)

        progress_label = tk.Label(progress_frame, text="0%",
                                font=("Segoe UI", 12), bg="#1a1a2e", fg="#ffffff")
        progress_label.pack(pady=5)
        
        def download_progress(block_count, block_size, total_size):
            if total_size > 0:
                downloaded = block_count * block_size
                progress_percentage = int((downloaded / total_size) * 100)
                progress_bar['value'] = progress_percentage
                progress_label['text'] = f"{progress_percentage}%"
                progress_frame.update()
        
        def download_and_extract():
            try:
                patch_files = get_patch_files("https://aion-blitz.online/aion-blitz/Game-Launcher/Patches/")
                if not patch_files:
                    messagebox.showinfo("Update Check", "No updates available.")
                    progress_frame.destroy()
                    self.update_status("No updates available")
                    return
                
                current_version, processed_files = get_current_version()
                filtered_patch_files = []

                if current_version is None:
                    for patch_file in sorted(patch_files, key=natural_sort_key):
                        try:
                            version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                            if version_match:
                                filtered_patch_files.append(patch_file)
                                break
                        except Exception:
                            continue
                    
                    if not filtered_patch_files:
                        messagebox.showinfo("Update Check", "No valid patch files found.")
                        progress_frame.destroy()
                        return
                else:
                    for patch_file in patch_files:
                        try:
                            version_match = re.search(r'patch_(\d+\.\d+\.\d+)', patch_file)
                            if version_match:
                                patch_version = version_match.group(1)
                                if version.parse(patch_version) > version.parse(current_version):
                                    filtered_patch_files.append(patch_file)
                        except (AttributeError, ValueError) as e:
                            print(f"Error parsing version from {patch_file}: {e}")
                            continue
                
                if not filtered_patch_files:
                    messagebox.showinfo("Update Check", "Your game is up to date!")
                    progress_frame.destroy()
                    self.update_status("Game is up to date")
                    return
                
                total_files = len(filtered_patch_files)
                for i, patch_file in enumerate(filtered_patch_files, start=1):
                    patch_url = "https://aion-blitz.online/aion-blitz/Game-Launcher/Patches/" + patch_file
                    patch_path = os.path.join(script_dir, patch_file)
                    req = urllib.request.Request(patch_url, headers={"User-Agent": "MyGameLauncher"})
                    with urllib.request.urlopen(req) as response, open(patch_path, 'wb') as out_file:
                        total_size = int(response.info().get('Content-Length', 0))
                        block_size = 8192
                        block_count = 0
                        while True:
                            buffer = response.read(block_size)
                            if not buffer:
                                break
                            out_file.write(buffer)
                            block_count += 1
                            download_progress(block_count, block_size, total_size)
                    
                    extract_patch(patch_path)
                    os.remove(patch_path)
                
                latest_patch = filtered_patch_files[-1]
                version_match = re.search(r'patch_(\d+\.\d+\.\d+)', latest_patch)
                if version_match:
                    latest_version = version_match.group(1)
                    save_version(latest_version)
                else:
                    raise ValueError(f"Could not parse version from patch file: {latest_patch}")
                
                progress_frame.destroy()
                messagebox.showinfo("Update Completed", "Updates have been applied successfully.")
                self.update_status("Updates completed successfully")
            except Exception as e:
                messagebox.showerror("Update Error", f"An error occurred during the update process: {str(e)}")
                progress_frame.destroy()
                self.update_status("Update failed")
        
        thread = threading.Thread(target=download_and_extract)
        thread.start()
    
    def run(self):
        self.window.mainloop()



# Download the background image with fallback
def download_background_image(url):
    filename = os.path.join(script_dir, "background_image.png")
    if not os.path.exists(filename):
        try:
            req = urllib.request.Request(url, headers={"User-Agent": "MyGameLauncher"})
            with urllib.request.urlopen(req) as response, open(filename, 'wb') as out_file:
                out_file.write(response.read())
        except Exception as e:
            print(f"Failed to download background image: {e}")

# Download background image on startup
download_background_image("https://aion-blitz.online/aion-blitz/Game-Launcher/Images/background_image.png")

# Alternative fantasy background if the original doesn't work
def create_fantasy_background():
    """Create a beautiful fantasy-themed background if download fails"""
    try:
        from PIL import Image, ImageDraw

        # Create a gradient background
        width, height = 1200, 800
        image = Image.new('RGB', (width, height), '#1a1a2e')
        draw = ImageDraw.Draw(image)

        # Create golden gradient effect
        for y in range(height):
            # Calculate color based on position
            progress = y / height
            r = int(26 + progress * 100)  # Dark to golden
            g = int(26 + progress * 80)   # Dark to amber
            b = int(46 + progress * 20)   # Keep blue low
            color = (r, g, b)
            draw.line([(0, y), (width, y)], fill=color)

        # Save the background
        bg_path = os.path.join(script_dir, "background_image.png")
        image.save(bg_path)
        print("Created fantasy background")

    except Exception as e:
        print(f"Could not create fantasy background: {e}")

# Create fantasy background if original doesn't exist
bg_path = os.path.join(script_dir, "background_image.png")
if not os.path.exists(bg_path):
    create_fantasy_background()

# Fetch cheat tool list
def fetch_cheat_tool_list():
    url = "https://aion-blitz.online/aion-blitz/Game-Launcher/anti-cheat/cheating.txt"
    headers = {"User-Agent": "MyGameLauncher"}
    try:
        response = requests.get(url, headers=headers, timeout=5)
        response.raise_for_status()
        cheat_data = response.text.splitlines()
        if not cheat_data:
            print("Cheat list is empty or invalid.")
        return cheat_data
    except requests.RequestException as e:
        print(f"Failed to fetch the cheat tool list: {e}")
        print("Continuing without anti-cheat monitoring...")
        return []

# Fetch the list of patch files from the server with custom User-Agent
def get_patch_files(url):
    try:
        req = urllib.request.Request(url, headers={"User-Agent": "MyGameLauncher"})
        response = urllib.request.urlopen(req)
        html = response.read().decode()
        patch_files = re.findall(r'href=\"([^\"]+\.zip)\"', html)
        return patch_files
    except Exception as e:
        print(f"Error fetching patch files: {e}")
        return []

def get_current_version():
    version_file = os.path.join(script_dir, "version.txt")
    if os.path.exists(version_file):
        try:
            with open(version_file, 'r') as f:
                content = f.read().strip()
                if not content:  # If file is empty
                    return None, []

                lines = content.split('\n')
                # First line is the semantic version (or "updated" for legacy)
                version_str = lines[0]
                # Remaining lines are processed non-versioned files
                processed_files = lines[1:] if len(lines) > 1 else []
                return version_str, processed_files
        except Exception as e:
            print(f"Error reading version file: {e}")
    return None, []

def save_version(version_str, processed_files=None):
    version_file = os.path.join(script_dir, "version.txt")
    try:
        with open(version_file, 'w') as f:
            f.write(version_str)
            if processed_files:
                for file in processed_files:
                    f.write(f'\n{file}')
    except Exception as e:
        print(f"Error saving version: {e}")

def extract_patch(patch_path):
    with zipfile.ZipFile(patch_path, 'r') as zip_ref:
        zip_ref.extractall(script_dir)

# Initialize cheat tools list
cheat_tools = fetch_cheat_tool_list()

# Create and run the launcher
if __name__ == "__main__":
    launcher = ModernLauncher()
    launcher.run()
